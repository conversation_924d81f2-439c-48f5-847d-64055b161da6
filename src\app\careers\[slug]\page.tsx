import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import JobPostingHeader from '@/components/sections/job-posting-header';
import JobPostingContent from '@/components/sections/job-posting-content';
import JobPostingApplication from '@/components/sections/job-posting-application';
import JobPostingSidebar from '@/components/sections/job-posting-sidebar';

interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'closed' | 'draft';
  publishedAt: string;
  updatedAt?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  duration?: string;
}

// Generate metadata for the job posting
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const job = await getJobPosting(resolvedParams.slug);

  if (!job) {
    return {
      title: 'Job Not Found - Lunar Cubes Careers',
      description: 'The job posting you are looking for could not be found.',
    };
  }

  return {
    title: `${job.title} - Careers at Lunar Cubes Nepal`,
    description: `Join our team as a ${job.title} in ${job.department}. ${job.description.substring(0, 150)}...`,
    keywords: `${job.title} jobs Nepal, ${job.department} careers Kathmandu, ${job.type} jobs Nepal, digital marketing careers Nepal`,
    openGraph: {
      title: `${job.title} - Careers at Lunar Cubes Nepal`,
      description: `Join our team as a ${job.title}. Apply now for this exciting opportunity in ${job.location}.`,
      url: `/careers/${job.slug}`,
    },
  };
}

// Get job posting data
async function getJobPosting(slug: string): Promise<JobPosting | null> {
  try {
    // In development, read from file system directly
    if (process.env.NODE_ENV === 'development') {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/jobs.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const jobs: JobPosting[] = JSON.parse(fileContents);
      return jobs.find((job: JobPosting) => job.slug === slug && job.status === 'active') || null;
    }

    // In production, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/jobs.json`, {
      cache: 'force-cache',
    });

    if (!response.ok) {
      return null;
    }

    const jobs: JobPosting[] = await response.json();
    return jobs.find((job: JobPosting) => job.slug === slug && job.status === 'active') || null;
  } catch (error) {
    console.error('Error fetching job posting:', error);
    return null;
  }
}

// Generate static params for all job postings
export async function generateStaticParams() {
  try {
    // In development, read from file system directly
    if (process.env.NODE_ENV === 'development') {
      const fs = await import('fs');
      const path = await import('path');
      const filePath = path.join(process.cwd(), 'public/data/jobs.json');
      const fileContents = fs.readFileSync(filePath, 'utf8');
      const jobs: JobPosting[] = JSON.parse(fileContents);
      return jobs
        .filter((job: JobPosting) => job.status === 'active')
        .map((job: JobPosting) => ({
          slug: job.slug,
        }));
    }

    // In production, fetch from URL
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/data/jobs.json`, {
      cache: 'force-cache',
    });

    if (!response.ok) {
      return [];
    }

    const jobs: JobPosting[] = await response.json();
    return jobs
      .filter((job: JobPosting) => job.status === 'active')
      .map((job: JobPosting) => ({
        slug: job.slug,
      }));
  } catch (error) {
    console.error('Error generating static params for jobs:', error);
    return [];
  }
}

interface JobPostingPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function JobPostingPage({ params }: JobPostingPageProps) {
  const resolvedParams = await params;
  const job = await getJobPosting(resolvedParams.slug);

  if (!job) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <JobPostingHeader job={job} />

      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <JobPostingContent job={job} />
            <JobPostingApplication job={job} />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <JobPostingSidebar job={job} />
          </div>
        </div>
      </div>
    </div>
  );
}
