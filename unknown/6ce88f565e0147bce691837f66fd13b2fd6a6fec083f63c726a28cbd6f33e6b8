import { NextRequest, NextResponse } from "next/server";
import {
  generateSitemapRoutes,
  generateSitemapXML,
  type SitemapConfig,
} from "@/lib/sitemap-generator";

export async function GET(request: NextRequest) {
  try {
    // Get the base URL from the request
    const baseUrl = `${request.nextUrl.protocol}//${request.nextUrl.host}`;

    // Generate routes
    const routes = generateSitemapRoutes();

    // Create sitemap configuration
    const config: SitemapConfig = {
      baseUrl,
      routes,
      excludePaths: ["/api", "/admin", "/_next"],
    };

    // Generate XML
    const sitemapXML = generateSitemapXML(config);

    // Return XML response with proper headers
    return new NextResponse(sitemapXML, {
      status: 200,
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600", // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return NextResponse.json(
      { error: "Failed to generate sitemap" },
      { status: 500 }
    );
  }
}
