'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON>R<PERSON>, MapPin, Clock, DollarSign, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useJobPostings } from '@/lib/queries/hooks';

const jobTypeColors = {
  'full-time': 'bg-green-100 text-green-800',
  'part-time': 'bg-blue-100 text-blue-800',
  'contract': 'bg-purple-100 text-purple-800',
  'internship': 'bg-orange-100 text-orange-800',
};

const departmentColors = {
  'Engineering': 'bg-blue-500',
  'Marketing': 'bg-green-500',
  'Design': 'bg-purple-500',
  'Sales': 'bg-orange-500',
  'Operations': 'bg-teal-500',
};

export default function OpenPositions() {
  const { data: jobs, isLoading } = useJobPostings();

  const activeJobs = jobs?.filter(job => job.status === 'active') || [];

  return (
    <section id="open-positions" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 font-heading">
            Open{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-brand-navy to-brand-gold">
              Positions
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join our team and help shape the future of digital marketing in Nepal.
            We&apos;re always looking for talented individuals who share our passion for excellence.
          </p>
        </motion.div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-gray-100 rounded-2xl p-8 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        )}

        {/* Job Listings */}
        {!isLoading && activeJobs.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {activeJobs.map((job, index) => (
              <motion.div
                key={job.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white border border-gray-200 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 group-hover:-translate-y-1 h-full">
                  {/* Department Badge */}
                  <div className="flex items-center justify-between mb-6">
                    <div
                      className={`w-3 h-3 rounded-full ${departmentColors[job.department] || 'bg-gray-500'}`}
                    ></div>
                    <Badge
                      variant="secondary"
                      className={`${jobTypeColors[job.type]} border-0 font-medium`}
                    >
                      {job.type.charAt(0).toUpperCase() + job.type.slice(1).replace('-', ' ')}
                    </Badge>
                  </div>

                  {/* Job Title */}
                  <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-brand-navy transition-colors">
                    {job.title}
                  </h3>

                  {/* Job Meta */}
                  <div className="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span>{job.department}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>{job.location}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{job.type.replace('-', ' ')}</span>
                    </div>
                    {job.salary && (
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        <span>
                          NPR {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()}/{job.salary.period}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Job Description */}
                  <p className="text-gray-600 mb-6 line-clamp-3">
                    {job.description}
                  </p>

                  {/* Key Requirements */}
                  {job.requirements && job.requirements.length > 0 && (
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Key Requirements:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {job.requirements.slice(0, 3).map((req, reqIndex) => (
                          <li key={reqIndex} className="flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-brand-gold rounded-full mt-2 flex-shrink-0"></span>
                            <span>{req}</span>
                          </li>
                        ))}
                        {job.requirements.length > 3 && (
                          <li className="text-brand-navy font-medium">
                            +{job.requirements.length - 3} more requirements
                          </li>
                        )}
                      </ul>
                    </div>
                  )}

                  {/* Apply Button */}
                  <Button
                    asChild
                    className="w-full bg-brand-navy hover:bg-brand-navy-dark text-white group-hover:bg-brand-gold group-hover:text-brand-navy transition-all duration-300"
                  >
                    <Link href={`/careers/${job.slug}`}>
                      View Details & Apply
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </motion.div>
            ))}
          </div>
        )}

        {/* No Jobs Available */}
        {!isLoading && activeJobs.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center py-16"
          >
            <div className="bg-gray-50 rounded-3xl p-12">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-gray-900 mb-4">No Open Positions Right Now</h3>
              <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
                We don&apos;t have any open positions at the moment, but we&apos;re always interested in
                connecting with talented individuals. Send us your resume and we&apos;ll keep you in mind
                for future opportunities.
              </p>
              <Button asChild size="lg" className="bg-brand-navy hover:bg-brand-navy-dark text-white">
                <Link href="/contact">
                  Send Your Resume
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </motion.div>
        )}
      </div>
    </section>
  );
}
